<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions for exact alarm scheduling and reboot handling -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <!-- 指定前台服务类型为媒体播放 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <!-- Android 13+ for posting notifications -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Android 13+ 读取系统铃声 -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <!-- 调整音量 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 网络访问权限，用于从 GitHub 拉取节假日数据 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Alarm_clock_2" >
        <service
            android:name=".alarm.AlarmService"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />

        <activity android:name=".alarm.AlarmActivity"
            android:theme="@style/Theme.Alarm_clock_2"
            android:exported="false"
            android:showOnLockScreen="true"
            android:turnScreenOn="true" />
        <!-- Alarm broadcast receiver -->
        <receiver android:name=".alarm.AlarmReceiver" android:exported="false" />

        <!-- Re-schedule alarms after device reboot -->
        <receiver
            android:name=".alarm.BootCompletedReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <activity android:name=".MainActivity" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Remove default WorkManager InitializationProvider because MyApplication provides Configuration.Provider -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            tools:node="remove" />

    </application>
</manifest>